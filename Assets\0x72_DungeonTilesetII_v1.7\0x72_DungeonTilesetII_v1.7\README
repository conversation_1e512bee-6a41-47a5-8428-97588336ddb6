= README

Autotilesets (both low and high) are designed according to
https://github.com/godotengine/godot-docs/issues/3316 (3x3 minimal).

The 16x16 atlas is designes so there is little setup needed in most
engines. Each tile is exactly 16x16 pixels and they are supposed to
be placed on 16x16 grid, no layers needed.

The 16x32 (high) set there most of the tiles are set with the Y offsed
of 8, some of them -2 (you'll figure which one is which, you can use
different values is the difference is 10). Some tiles are meant to be
drawn on a layer above (so they modify the one below). The tile size is
bigger than the grid that they are supposed to be placed on (16x16).

