"""
Asset management system for loading and managing game sprites and resources.

This module handles loading all game assets including character sprites,
bullet sprites, enemy sprites, and tilesets with proper error handling.
"""

import pygame
import os
from typing import List
from config import Config


class AssetManager:
    """Handles loading and managing game assets"""
    
    def __init__(self):
        self.character_sprites = []
        self.bullet_image = None
        self.goblin_frames = []
        self.goblin_frames_flipped = []
        self.grass_tiles = []
        self.tree_tile = None
    
    def load_all_assets(self):
        """Load all game assets"""
        print("Loading game assets...")
        self.load_character_sprites()
        self.load_bullet_sprite()
        self.load_goblin_sprites()
        self.load_tileset()
        print("All assets loaded successfully!")
    
    def load_character_sprites(self):
        """Load and process character sprite sheet"""
        if not os.path.exists(Config.CHARACTER_SPRITE_PATH):
            raise FileNotFoundError(f"Character sprite file not found: {Config.CHARACTER_SPRITE_PATH}")

        character_sheet = pygame.image.load(Config.CHARACTER_SPRITE_PATH).convert_alpha()

        # Extract sprites from the specific section of the sheet
        self.character_sprites = []
        for row in range(Config.SPRITE_ROWS):  # 3 animation frames
            sprite_row = []
            for col in range(Config.SPRITE_COLS):  # 8 directions
                x = Config.SPRITE_START_X + (col * Config.SPRITE_WIDTH)
                y = Config.SPRITE_START_Y + (row * Config.SPRITE_HEIGHT)
                sprite = character_sheet.subsurface(
                    pygame.Rect(x, y, Config.SPRITE_WIDTH, Config.SPRITE_HEIGHT)
                )
                sprite_row.append(sprite)
            self.character_sprites.append(sprite_row)
    
    def load_bullet_sprite(self):
        """Load bullet sprite"""
        if not os.path.exists(Config.BULLET_SPRITE_PATH):
            raise FileNotFoundError(f"Bullet sprite file not found: {Config.BULLET_SPRITE_PATH}")
        
        self.bullet_image = pygame.image.load(Config.BULLET_SPRITE_PATH).convert_alpha()
    
    def load_goblin_sprites(self):
        """Load and process goblin sprite sheet"""
        if not os.path.exists(Config.GOBLIN_SPRITE_PATH):
            raise FileNotFoundError(f"Goblin sprite file not found: {Config.GOBLIN_SPRITE_PATH}")
        
        goblin_sheet = pygame.image.load(Config.GOBLIN_SPRITE_PATH).convert_alpha()
        
        # Extract goblin frames (assuming horizontal layout)
        self.goblin_frames = []
        for i in range(Config.GOBLIN_FRAMES):
            x = i * Config.GOBLIN_WIDTH
            y = 0
            frame = goblin_sheet.subsurface(
                pygame.Rect(x, y, Config.GOBLIN_WIDTH, Config.GOBLIN_HEIGHT)
            )
            self.goblin_frames.append(frame)
        
        # Create flipped versions for left movement
        self.goblin_frames_flipped = []
        for frame in self.goblin_frames:
            flipped_frame = pygame.transform.flip(frame, True, False)  # Flip horizontally
            self.goblin_frames_flipped.append(flipped_frame)
    
    def load_tileset(self):
        """Load and process tileset"""
        if not os.path.exists(Config.TILESET_PATH):
            raise FileNotFoundError(f"Tileset file not found: {Config.TILESET_PATH}")
        
        tileset = pygame.image.load(Config.TILESET_PATH).convert_alpha()
        
        # Extract grass tiles (all columns except the rightmost)
        self.grass_tiles = []
        tileset_width = tileset.get_width()
        tileset_height = tileset.get_height()
        
        tiles_per_row = tileset_width // Config.TILE_SIZE
        tiles_per_col = tileset_height // Config.TILE_SIZE
        
        for row in range(tiles_per_col):
            for col in range(tiles_per_row - 1):  # Exclude rightmost column (trees)
                x = col * Config.TILE_SIZE
                y = row * Config.TILE_SIZE
                tile = tileset.subsurface(
                    pygame.Rect(x, y, Config.TILE_SIZE, Config.TILE_SIZE)
                )
                self.grass_tiles.append(tile)
        
        # Extract tree tile (rightmost column, first row)
        tree_x = (tiles_per_row - 1) * Config.TILE_SIZE
        tree_y = 0
        self.tree_tile = tileset.subsurface(
            pygame.Rect(tree_x, tree_y, Config.TILE_SIZE, Config.TILE_SIZE)
        )
        
        # Set transparency for tree tile using corner pixel as alpha key
        corner_color = self.tree_tile.get_at((0, 0))
        self.tree_tile.set_colorkey(corner_color)
    
    def get_character_sprites(self) -> List[List[pygame.Surface]]:
        """Get character sprite array"""
        return self.character_sprites
    
    def get_bullet_image(self) -> pygame.Surface:
        """Get bullet sprite"""
        return self.bullet_image
    
    def get_goblin_frames(self) -> tuple:
        """Get goblin animation frames (normal and flipped)"""
        return self.goblin_frames, self.goblin_frames_flipped
    
    def get_tileset_assets(self) -> tuple:
        """Get tileset assets (grass tiles and tree tile)"""
        return self.grass_tiles, self.tree_tile
