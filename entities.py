"""
Game entity classes including <PERSON>, Bullet, Goblin, and Particle.

This module contains all the game entity classes with their behavior,
animation, and interaction logic.
"""

import pygame
import random
import math
from typing import List, Tuple, Callable
from config import Config
from physics import (
    check_bounds_collision, 
    calculate_direction_vector, 
    is_off_screen,
    apply_deceleration,
    generate_random_velocity
)


class Player:
    """Player character with movement and animation"""
    
    def __init__(self, x: float, y: float, character_sprites: List[List[pygame.Surface]]):
        self.x = float(x)
        self.y = float(y)
        self.character_sprites = character_sprites
        self.direction = Config.DIRECTION_DOWN
        self.animation_frame = 0
        self.animation_timer = 0
        self.current_animation_frame = 0
    
    def update(self, keys_pressed: pygame.key.ScancodeWrapper, tree_collision_func: Callable) -> bool:
        """
        Update player position and animation based on input.
        
        Args:
            keys_pressed: Pygame keys state
            tree_collision_func: Function to check tree collisions
        
        Returns:
            True if player is moving, False otherwise
        """
        moving = False
        original_x, original_y = self.x, self.y
        
        # WASD movement
        if keys_pressed[pygame.K_w]:  # W - Move up
            new_y = self.y - Config.CHARACTER_SPEED
            if not tree_collision_func(self.x, new_y, Config.SPRITE_WIDTH, Config.SPRITE_HEIGHT):
                self.y = new_y
                moving = True
            self.direction = Config.DIRECTION_UP
        
        if keys_pressed[pygame.K_s]:  # S - Move down
            new_y = self.y + Config.CHARACTER_SPEED
            if not tree_collision_func(self.x, new_y, Config.SPRITE_WIDTH, Config.SPRITE_HEIGHT):
                self.y = new_y
                moving = True
            self.direction = Config.DIRECTION_DOWN
        
        if keys_pressed[pygame.K_a]:  # A - Move left
            new_x = self.x - Config.CHARACTER_SPEED
            if not tree_collision_func(new_x, self.y, Config.SPRITE_WIDTH, Config.SPRITE_HEIGHT):
                self.x = new_x
                moving = True
            self.direction = Config.DIRECTION_LEFT
        
        if keys_pressed[pygame.K_d]:  # D - Move right
            new_x = self.x + Config.CHARACTER_SPEED
            if not tree_collision_func(new_x, self.y, Config.SPRITE_WIDTH, Config.SPRITE_HEIGHT):
                self.x = new_x
                moving = True
            self.direction = Config.DIRECTION_RIGHT
        
        # Keep player within screen bounds
        self.x, self.y = check_bounds_collision(self.x, self.y, Config.SPRITE_WIDTH, Config.SPRITE_HEIGHT)
        
        # Update animation
        if moving:
            self.animation_timer += 1
            if self.animation_timer >= Config.ANIMATION_SPEED:
                self.animation_timer = 0
                self.current_animation_frame = 1 - self.current_animation_frame  # Toggle between 0 and 1
            self.animation_frame = 0 if self.current_animation_frame == 0 else 2
        else:
            self.animation_frame = 1  # Stationary frame
            self.animation_timer = 0
        
        return moving
    
    def get_center(self) -> Tuple[float, float]:
        """Get the center position of the player"""
        center_x = self.x + Config.SPRITE_WIDTH // 2
        center_y = self.y + Config.SPRITE_HEIGHT // 2
        return center_x, center_y
    
    def draw(self, screen: pygame.Surface):
        """Draw the player sprite"""
        current_sprite = self.character_sprites[self.animation_frame][self.direction]
        screen.blit(current_sprite, (int(self.x), int(self.y)))


class Bullet:
    """Projectile fired by the player"""
    
    def __init__(self, start_x: float, start_y: float, target_x: float, target_y: float, bullet_image: pygame.Surface):
        self.x = float(start_x)
        self.y = float(start_y)
        self.image = bullet_image
        self.width = bullet_image.get_width()
        self.height = bullet_image.get_height()
        
        # Calculate direction and velocity
        direction_x, direction_y = calculate_direction_vector(start_x, start_y, target_x, target_y)
        self.vel_x = direction_x * Config.BULLET_SPEED
        self.vel_y = direction_y * Config.BULLET_SPEED
    
    def update(self):
        """Update bullet position"""
        self.x += self.vel_x
        self.y += self.vel_y
    
    def is_off_screen(self) -> bool:
        """Check if bullet is off screen"""
        return is_off_screen(self.x, self.y, self.width, self.height)
    
    def get_rect(self) -> pygame.Rect:
        """Get collision rectangle"""
        return pygame.Rect(int(self.x), int(self.y), self.width, self.height)
    
    def draw(self, screen: pygame.Surface):
        """Draw the bullet"""
        screen.blit(self.image, (int(self.x), int(self.y)))


class Goblin:
    """Enemy that chases the player"""

    def __init__(self, x: float, y: float, frames: List[pygame.Surface], frames_flipped: List[pygame.Surface]):
        self.x = float(x)
        self.y = float(y)
        self.speed = Config.GOBLIN_SPEED
        self.facing_left = False
        self.animation_frame = 0
        self.animation_timer = 0
        self.frames = frames
        self.frames_flipped = frames_flipped

        # Size properties (assuming all frames have same size)
        self.width = frames[0].get_width()
        self.height = frames[0].get_height()

    def update(self, player_x: float, player_y: float):
        """Update goblin position and animation to chase player"""
        # Calculate direction to player
        direction_x, direction_y = calculate_direction_vector(self.x, self.y, player_x, player_y)

        # Update position
        self.x += direction_x * self.speed
        self.y += direction_y * self.speed

        # Determine facing direction based on horizontal movement
        if direction_x < 0 and not self.facing_left:
            self.facing_left = True
        elif direction_x > 0 and self.facing_left:
            self.facing_left = False

        # Update animation
        self.animation_timer += 1
        if self.animation_timer >= Config.GOBLIN_ANIMATION_SPEED:
            self.animation_timer = 0
            self.animation_frame = (self.animation_frame + 1) % len(self.frames)

    def get_rect(self) -> pygame.Rect:
        """Get collision rectangle"""
        return pygame.Rect(int(self.x), int(self.y), self.width, self.height)

    def draw(self, screen: pygame.Surface):
        """Draw the goblin with correct facing direction"""
        if self.facing_left:
            current_frame = self.frames_flipped[self.animation_frame]
        else:
            current_frame = self.frames[self.animation_frame]
        screen.blit(current_frame, (int(self.x), int(self.y)))


class Particle:
    """Explosion particle effect"""

    def __init__(self, x: float, y: float):
        self.x = float(x)
        self.y = float(y)

        # Generate random velocity
        self.vel_x, self.vel_y = generate_random_velocity(
            Config.PARTICLE_MIN_SPEED,
            Config.PARTICLE_MAX_SPEED
        )

        # Random size and lifetime
        self.size = random.randint(Config.PARTICLE_MIN_SIZE, Config.PARTICLE_MAX_SIZE)
        self.lifetime = random.randint(Config.PARTICLE_MIN_LIFETIME, Config.PARTICLE_MAX_LIFETIME)
        self.max_lifetime = self.lifetime

    def update(self):
        """Update particle position and lifetime"""
        self.x += self.vel_x
        self.y += self.vel_y
        # Apply deceleration
        self.vel_x, self.vel_y = apply_deceleration(self.vel_x, self.vel_y)
        self.lifetime -= 1

    def is_dead(self) -> bool:
        """Check if particle should be removed"""
        return self.lifetime <= 0

    def draw(self, screen: pygame.Surface):
        """Draw the particle with fading effect"""
        # Fade out as lifetime decreases
        alpha = int(255 * (self.lifetime / self.max_lifetime))
        color = (*Config.RED, alpha)

        # Create a surface with per-pixel alpha for the particle
        particle_surface = pygame.Surface((self.size * 2, self.size * 2), pygame.SRCALPHA)
        pygame.draw.circle(particle_surface, color, (self.size, self.size), self.size)
        screen.blit(particle_surface, (int(self.x - self.size), int(self.y - self.size)))
