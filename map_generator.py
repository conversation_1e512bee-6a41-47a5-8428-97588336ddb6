"""
Map generation system for creating game levels.

This module handles the generation of the game map including background tiles,
tree placement, and spawn point creation.
"""

import random
from typing import List, Set, Tuple
import pygame
from config import Config


class MapGenerator:
    """Handles map generation and management"""
    
    def __init__(self, grass_tiles: List[pygame.Surface]):
        self.grass_tiles = grass_tiles
        self.background_map = []
        self.tree_positions = []
        self.tree_grid_positions = set()
        self.spawn_points = []
        
    def generate_map(self):
        """Generate the complete game map"""
        print("Generating game map...")
        self._generate_background()
        self._generate_trees()
        self._generate_spawn_points()
        print(f"Map generated: {len(self.tree_positions)} trees, {len(self.spawn_points)} spawn points")
    
    def _generate_background(self):
        """Generate random grass background"""
        self.background_map = []
        for row in range(Config.MAP_HEIGHT):
            map_row = []
            for col in range(Config.MAP_WIDTH):
                # Choose random grass tile
                grass_tile_index = random.randint(0, len(self.grass_tiles) - 1)
                map_row.append(grass_tile_index)
            self.background_map.append(map_row)
    
    def _generate_trees(self):
        """Generate tree border with gaps for spawn points"""
        self.tree_positions = []
        self.tree_grid_positions = set()
        
        # Calculate center positions for each edge (3 squares wide gap)
        center_top = Config.MAP_WIDTH // 2
        center_bottom = Config.MAP_WIDTH // 2
        center_left = Config.MAP_HEIGHT // 2
        center_right = Config.MAP_HEIGHT // 2
        
        # Top edge (y = 0)
        for x in range(Config.MAP_WIDTH):
            if not (center_top - 1 <= x <= center_top + 1):  # Skip 3 center squares
                self.tree_grid_positions.add((x, 0))
                self.tree_positions.append((x * Config.TILE_SIZE, 0))
        
        # Bottom edge (y = MAP_HEIGHT - 1)
        for x in range(Config.MAP_WIDTH):
            if not (center_bottom - 1 <= x <= center_bottom + 1):  # Skip 3 center squares
                self.tree_grid_positions.add((x, Config.MAP_HEIGHT - 1))
                self.tree_positions.append((x * Config.TILE_SIZE, (Config.MAP_HEIGHT - 1) * Config.TILE_SIZE))
        
        # Left edge (x = 0)
        for y in range(1, Config.MAP_HEIGHT - 1):  # Skip corners (already covered by top/bottom)
            if not (center_left - 1 <= y <= center_left + 1):  # Skip 3 center squares
                self.tree_grid_positions.add((0, y))
                self.tree_positions.append((0, y * Config.TILE_SIZE))
        
        # Right edge (x = MAP_WIDTH - 1)
        for y in range(1, Config.MAP_HEIGHT - 1):  # Skip corners (already covered by top/bottom)
            if not (center_right - 1 <= y <= center_right + 1):  # Skip 3 center squares
                self.tree_grid_positions.add((Config.MAP_WIDTH - 1, y))
                self.tree_positions.append(((Config.MAP_WIDTH - 1) * Config.TILE_SIZE, y * Config.TILE_SIZE))
    
    def _generate_spawn_points(self):
        """Generate spawn points at the center of each gap"""
        self.spawn_points = [
            # Top gap
            (Config.MAP_WIDTH // 2 * Config.TILE_SIZE, 0),
            # Bottom gap
            (Config.MAP_WIDTH // 2 * Config.TILE_SIZE, (Config.MAP_HEIGHT - 1) * Config.TILE_SIZE),
            # Left gap
            (0, Config.MAP_HEIGHT // 2 * Config.TILE_SIZE),
            # Right gap
            ((Config.MAP_WIDTH - 1) * Config.TILE_SIZE, Config.MAP_HEIGHT // 2 * Config.TILE_SIZE)
        ]
    
    def get_background_map(self) -> List[List[int]]:
        """Get the background tile map"""
        return self.background_map
    
    def get_tree_positions(self) -> List[Tuple[int, int]]:
        """Get list of tree positions in pixel coordinates"""
        return self.tree_positions
    
    def get_tree_grid_positions(self) -> Set[Tuple[int, int]]:
        """Get set of tree positions in grid coordinates"""
        return self.tree_grid_positions
    
    def get_spawn_points(self) -> List[Tuple[int, int]]:
        """Get list of spawn points in pixel coordinates"""
        return self.spawn_points
    
    def is_position_blocked(self, grid_x: int, grid_y: int) -> bool:
        """Check if a grid position is blocked by a tree"""
        return (grid_x, grid_y) in self.tree_grid_positions
    
    def get_random_spawn_point(self) -> Tuple[int, int]:
        """Get a random spawn point"""
        return random.choice(self.spawn_points)
    
    def get_map_bounds(self) -> Tuple[int, int]:
        """Get map dimensions in pixels"""
        return Config.WINDOW_WIDTH, Config.WINDOW_HEIGHT
    
    def get_tile_at_position(self, pixel_x: int, pixel_y: int) -> Tuple[int, int]:
        """
        Convert pixel coordinates to grid coordinates.
        
        Args:
            pixel_x: X position in pixels
            pixel_y: Y position in pixels
        
        Returns:
            Tuple of (grid_x, grid_y)
        """
        grid_x = pixel_x // Config.TILE_SIZE
        grid_y = pixel_y // Config.TILE_SIZE
        return grid_x, grid_y
    
    def is_valid_position(self, pixel_x: int, pixel_y: int, width: int, height: int) -> bool:
        """
        Check if a rectangular area is valid (not colliding with trees).
        
        Args:
            pixel_x: X position in pixels
            pixel_y: Y position in pixels
            width: Width of the area
            height: Height of the area
        
        Returns:
            True if position is valid, False otherwise
        """
        # Convert to grid coordinates
        left_grid = pixel_x // Config.TILE_SIZE
        right_grid = (pixel_x + width - 1) // Config.TILE_SIZE
        top_grid = pixel_y // Config.TILE_SIZE
        bottom_grid = (pixel_y + height - 1) // Config.TILE_SIZE
        
        # Check all grid positions the area occupies
        for grid_x in range(left_grid, right_grid + 1):
            for grid_y in range(top_grid, bottom_grid + 1):
                if self.is_position_blocked(grid_x, grid_y):
                    return False
        return True
