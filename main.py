"""
Top-down shooter game with character movement, shooting mechanics, and enemy AI.

This game features:
- WASD movement with 8-directional character sprites
- Mouse-click shooting with projectile physics
- Enemy goblins that chase the player
- Collision detection between bullets and enemies
- Particle explosion effects on enemy destruction
- Tree obstacles and collision detection
- Procedurally generated grass background

Controls:
- WASD: Move character
- Mouse click: Shoot towards cursor
- ESC: Exit game
"""

import pygame
import sys
import random
from typing import List

# Import our modular components
from config import Config
from assets import AssetManager
from entities import Player, Bullet, Goblin, Particle
from physics import check_tree_collision, create_explosion_particles
from map_generator import MapGenerator


# =============================================================================
# MAIN GAME CLASS
# =============================================================================

class Game:
    """Main game class that manages the game loop and state"""
    
    def __init__(self):
        pygame.init()
        self.screen = pygame.display.set_mode((Config.WINDOW_WIDTH, Config.WINDOW_HEIGHT))
        pygame.display.set_caption("Top Down Shooter")
        self.clock = pygame.time.Clock()
        self.running = True
        
        # Initialize asset manager and load assets
        self.asset_manager = AssetManager()
        self.asset_manager.load_all_assets()
        
        # Initialize map generator and generate map
        self.map_generator = MapGenerator(self.asset_manager.grass_tiles)
        self.map_generator.generate_map()
        
        # Initialize player at center of screen
        player_start_x = Config.WINDOW_WIDTH // 2 - Config.SPRITE_WIDTH // 2
        player_start_y = Config.WINDOW_HEIGHT // 2 - Config.SPRITE_HEIGHT // 2
        self.player = Player(player_start_x, player_start_y, self.asset_manager.character_sprites)
        
        # Game objects
        self.bullets = []
        self.goblins = []
        self.particles = []
        
        # Game state
        self.goblin_spawn_timer = 0
    
    def handle_events(self):
        """Handle pygame events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.running = False
            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # Left mouse button
                    self.shoot_bullet(event.pos)
    
    def shoot_bullet(self, target_pos):
        """Create a bullet towards the target position"""
        player_center = self.player.get_center()
        bullet = Bullet(
            player_center[0], 
            player_center[1], 
            target_pos[0], 
            target_pos[1], 
            self.asset_manager.bullet_image
        )
        self.bullets.append(bullet)
    
    def spawn_goblin(self):
        """Spawn a goblin at a random spawn point"""
        spawn_point = self.map_generator.get_random_spawn_point()
        goblin = Goblin(
            spawn_point[0], 
            spawn_point[1], 
            self.asset_manager.goblin_frames, 
            self.asset_manager.goblin_frames_flipped
        )
        self.goblins.append(goblin)
    
    def update(self):
        """Update all game objects"""
        keys_pressed = pygame.key.get_pressed()
        
        # Create collision function for player
        tree_collision_func = lambda x, y, w, h: check_tree_collision(
            x, y, w, h, self.map_generator.get_tree_grid_positions()
        )
        
        # Update player
        self.player.update(keys_pressed, tree_collision_func)
        
        # Update bullets
        for bullet in self.bullets[:]:
            bullet.update()
            if bullet.is_off_screen():
                self.bullets.remove(bullet)
        
        # Update goblins
        player_center = self.player.get_center()
        for goblin in self.goblins:
            goblin.update(player_center[0], player_center[1])
        
        # Update particles
        for particle in self.particles[:]:
            particle.update()
            if particle.is_dead():
                self.particles.remove(particle)
        
        # Check bullet-goblin collisions
        for bullet in self.bullets[:]:
            for goblin in self.goblins[:]:
                if bullet.get_rect().colliderect(goblin.get_rect()):
                    # Create explosion particles at goblin position
                    explosion_particles = create_explosion_particles(
                        goblin.x + goblin.width // 2,
                        goblin.y + goblin.height // 2
                    )
                    self.particles.extend(explosion_particles)
                    
                    # Remove bullet and goblin
                    self.bullets.remove(bullet)
                    self.goblins.remove(goblin)
                    break
        
        # Spawn goblins periodically
        self.goblin_spawn_timer += 1
        if self.goblin_spawn_timer >= Config.GOBLIN_SPAWN_INTERVAL:
            self.goblin_spawn_timer = 0
            self.spawn_goblin()
    
    def draw_background(self):
        """Draw the grass background"""
        background_map = self.map_generator.get_background_map()
        for row in range(len(background_map)):
            for col in range(len(background_map[row])):
                tile_index = background_map[row][col]
                grass_tile = self.asset_manager.grass_tiles[tile_index]
                x = col * Config.TILE_SIZE
                y = row * Config.TILE_SIZE
                self.screen.blit(grass_tile, (x, y))
    
    def draw_trees(self):
        """Draw the tree obstacles"""
        tree_positions = self.map_generator.get_tree_positions()
        for tree_pos in tree_positions:
            self.screen.blit(self.asset_manager.tree_tile, tree_pos)
    
    def draw(self):
        """Draw all game objects"""
        # Clear screen
        self.screen.fill(Config.BLACK)
        
        # Draw background
        self.draw_background()
        
        # Draw trees
        self.draw_trees()
        
        # Draw player
        self.player.draw(self.screen)
        
        # Draw bullets
        for bullet in self.bullets:
            bullet.draw(self.screen)
        
        # Draw goblins
        for goblin in self.goblins:
            goblin.draw(self.screen)
        
        # Draw particles
        for particle in self.particles:
            particle.draw(self.screen)
        
        # Update display
        pygame.display.flip()
    
    def run(self):
        """Main game loop"""
        while self.running:
            self.handle_events()
            self.update()
            self.draw()
            self.clock.tick(Config.FPS)
        
        pygame.quit()


# =============================================================================
# MAIN FUNCTION
# =============================================================================

def main():
    """Main function to start the game"""
    try:
        game = Game()
        game.run()
    except Exception as e:
        print(f"Error running game: {e}")
        pygame.quit()
        sys.exit(1)


if __name__ == "__main__":
    main()
