"""
Top Down Shooter Game

A pygame-based top-down shooter with character movement, shooting mechanics,
enemy AI, and particle effects.
"""

import pygame
import sys
import os
import random
import math
from typing import List, Tuple


# =============================================================================
# CONFIGURATION CONSTANTS
# =============================================================================

class Config:
    """Game configuration constants"""

    # Display settings
    TILE_SIZE = 16
    TILES_WIDTH = 600 // TILE_SIZE + 2  # Original width in tiles + border trees
    TILES_HEIGHT = 400 // TILE_SIZE + 2  # Original height in tiles + border trees
    WINDOW_WIDTH = TILES_WIDTH * TILE_SIZE
    WINDOW_HEIGHT = TILES_HEIGHT * TILE_SIZE

    # Map dimensions
    MAP_WIDTH = TILES_WIDTH
    MAP_HEIGHT = TILES_HEIGHT

    # Colors
    BLACK = (0, 0, 0)
    WHITE = (255, 255, 255)
    RED = (255, 0, 0)

    # Character settings
    SPRITE_START_X = 1
    SPRITE_START_Y = 374
    SPRITE_SECTION_WIDTH = 240
    SPRITE_SECTION_HEIGHT = 125
    SPRITE_COLS = 8  # 8 directions
    SPRITE_ROWS = 3  # 3 animation frames
    SPRITE_WIDTH = SPRITE_SECTION_WIDTH // SPRITE_COLS
    SPRITE_HEIGHT = SPRITE_SECTION_HEIGHT // SPRITE_ROWS
    CHARACTER_SPEED = 3

    # Animation settings
    ANIMATION_SPEED = 10
    GOBLIN_ANIMATION_SPEED = 8

    # Gameplay settings
    BULLET_SPEED = 8
    GOBLIN_SPEED = 1.5
    GOBLIN_SPAWN_INTERVAL = 120  # 2 seconds at 60 FPS
    FPS = 60

    # Directions (clockwise from up)
    DIRECTION_UP = 0
    DIRECTION_RIGHT = 2
    DIRECTION_DOWN = 4
    DIRECTION_LEFT = 6

    # Particle settings
    PARTICLE_MIN_COUNT = 8
    PARTICLE_MAX_COUNT = 15
    PARTICLE_MIN_SPEED = 2
    PARTICLE_MAX_SPEED = 6
    PARTICLE_MIN_SIZE = 2
    PARTICLE_MAX_SIZE = 5
    PARTICLE_MIN_LIFETIME = 20
    PARTICLE_MAX_LIFETIME = 40

    # Asset paths
    SPRITESHEET_PATH = os.path.join("Assets", "spritesheet.png")
    BULLET_PATH = os.path.join("Assets", "bullet.png")
    GOBLIN_PATH = os.path.join("Assets", "goblin.png")
    GRASS_TILESET_PATH = os.path.join("Assets", "Grass.png")


# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def apply_color_key_with_tolerance(surface: pygame.Surface, key_color: Tuple[int, int, int], tolerance: int = 30) -> pygame.Surface:
    """Apply color key transparency with tolerance for similar colors"""
    surface = surface.convert_alpha()
    width, height = surface.get_size()

    for x in range(width):
        for y in range(height):
            pixel_color = surface.get_at((x, y))

            # Calculate color difference (Manhattan distance in RGB space)
            color_diff = (abs(pixel_color[0] - key_color[0]) +
                         abs(pixel_color[1] - key_color[1]) +
                         abs(pixel_color[2] - key_color[2]))

            # If color is similar enough to key color, make it transparent
            if color_diff <= tolerance:
                surface.set_at((x, y), (pixel_color[0], pixel_color[1], pixel_color[2], 0))

    return surface

# =============================================================================
# ASSET MANAGEMENT
# =============================================================================

class AssetManager:
    """Handles loading and managing game assets"""

    def __init__(self):
        self.character_sprites = []
        self.bullet_image = None
        self.goblin_frames = []
        self.goblin_frames_flipped = []
        self.grass_tiles = []
        self.tree_tile = None

    def load_all_assets(self):
        """Load all game assets"""
        self.load_character_sprites()
        self.load_bullet_sprite()
        self.load_goblin_sprites()
        self.load_tileset()

    def load_character_sprites(self):
        """Load and process character sprites from spritesheet"""
        spritesheet = pygame.image.load(Config.SPRITESHEET_PATH)

        self.character_sprites = []
        for row in range(Config.SPRITE_ROWS):
            sprite_row = []
            for col in range(Config.SPRITE_COLS):
                x = Config.SPRITE_START_X + col * Config.SPRITE_WIDTH
                y = Config.SPRITE_START_Y + row * Config.SPRITE_HEIGHT
                sprite_rect = pygame.Rect(x, y, Config.SPRITE_WIDTH, Config.SPRITE_HEIGHT)
                sprite = spritesheet.subsurface(sprite_rect).copy()

                # Apply transparency
                corner_color = sprite.get_at((0, 0))
                sprite = apply_color_key_with_tolerance(sprite, corner_color, tolerance=30)

                sprite_row.append(sprite)
            self.character_sprites.append(sprite_row)

    def load_bullet_sprite(self):
        """Load and process bullet sprite"""
        self.bullet_image = pygame.image.load(Config.BULLET_PATH).convert_alpha()
        corner_color = self.bullet_image.get_at((0, 0))
        self.bullet_image = apply_color_key_with_tolerance(self.bullet_image, corner_color, tolerance=30)

    def load_goblin_sprites(self):
        """Load and process goblin sprites"""
        goblin_spritesheet = pygame.image.load(Config.GOBLIN_PATH)

        # Extract goblin animation frames (8 frames, 1 row)
        goblin_frames_count = 8
        goblin_width = goblin_spritesheet.get_width() // goblin_frames_count
        goblin_height = goblin_spritesheet.get_height()

        self.goblin_frames = []
        for frame in range(goblin_frames_count):
            x = frame * goblin_width
            frame_rect = pygame.Rect(x, 0, goblin_width, goblin_height)
            frame_surface = goblin_spritesheet.subsurface(frame_rect).copy()

            # Apply transparency
            corner_color = frame_surface.get_at((0, 0))
            frame_surface = apply_color_key_with_tolerance(frame_surface, corner_color, tolerance=30)

            self.goblin_frames.append(frame_surface)

        # Create flipped versions for left movement
        self.goblin_frames_flipped = []
        for frame in self.goblin_frames:
            flipped_frame = pygame.transform.flip(frame, True, False)
            self.goblin_frames_flipped.append(flipped_frame)

    def load_tileset(self):
        """Load and process grass tileset"""
        grass_tileset = pygame.image.load(Config.GRASS_TILESET_PATH)

        # Calculate tiles per row/column
        tiles_per_row = grass_tileset.get_width() // Config.TILE_SIZE
        tiles_per_col = grass_tileset.get_height() // Config.TILE_SIZE

        # Extract grass tiles (all columns except the rightmost)
        self.grass_tiles = []
        for row in range(tiles_per_col):
            for col in range(tiles_per_row - 1):  # Exclude rightmost column (trees)
                x = col * Config.TILE_SIZE
                y = row * Config.TILE_SIZE
                tile_rect = pygame.Rect(x, y, Config.TILE_SIZE, Config.TILE_SIZE)
                tile = grass_tileset.subsurface(tile_rect)
                self.grass_tiles.append(tile)

        # Extract tree tile (top row of rightmost column)
        tree_x = (tiles_per_row - 1) * Config.TILE_SIZE
        tree_y = 0
        tree_rect = pygame.Rect(tree_x, tree_y, Config.TILE_SIZE, Config.TILE_SIZE)
        self.tree_tile = grass_tileset.subsurface(tree_rect)

# =============================================================================
# GAME ENTITY CLASSES
# =============================================================================

class Player:
    """Player character with movement and animation"""

    def __init__(self, x: float, y: float, sprites: List[List[pygame.Surface]]):
        self.x = x
        self.y = y
        self.direction = Config.DIRECTION_DOWN
        self.frame = 1  # Stationary state (middle row)
        self.speed = Config.CHARACTER_SPEED
        self.sprites = sprites

        # Animation variables
        self.animation_timer = 0
        self.current_animation_frame = 0

        # Size properties
        self.width = Config.SPRITE_WIDTH
        self.height = Config.SPRITE_HEIGHT

    def update(self, keys_pressed: pygame.key.ScancodeWrapper, tree_collision_func) -> bool:
        """Update player position and animation. Returns True if moving."""
        moving = False

        # WASD movement with collision detection
        if keys_pressed[pygame.K_w]:  # W - Move up
            new_y = self.y - self.speed
            if not tree_collision_func(self.x, new_y, self.width, self.height):
                self.y = new_y
                moving = True
            self.direction = Config.DIRECTION_UP

        if keys_pressed[pygame.K_s]:  # S - Move down
            new_y = self.y + self.speed
            if not tree_collision_func(self.x, new_y, self.width, self.height):
                self.y = new_y
                moving = True
            self.direction = Config.DIRECTION_DOWN

        if keys_pressed[pygame.K_a]:  # A - Move left
            new_x = self.x - self.speed
            if not tree_collision_func(new_x, self.y, self.width, self.height):
                self.x = new_x
                moving = True
            self.direction = Config.DIRECTION_LEFT

        if keys_pressed[pygame.K_d]:  # D - Move right
            new_x = self.x + self.speed
            if not tree_collision_func(new_x, self.y, self.width, self.height):
                self.x = new_x
                moving = True
            self.direction = Config.DIRECTION_RIGHT

        # Keep player within screen bounds
        self.x = max(0, min(self.x, Config.WINDOW_WIDTH - self.width))
        self.y = max(0, min(self.y, Config.WINDOW_HEIGHT - self.height))

        # Update animation
        if moving:
            self.animation_timer += 1
            if self.animation_timer >= Config.ANIMATION_SPEED:
                self.animation_timer = 0
                self.current_animation_frame = 1 - self.current_animation_frame
            self.frame = 0 if self.current_animation_frame == 0 else 2
        else:
            self.frame = 1
            self.animation_timer = 0

        return moving

    def get_center(self) -> Tuple[float, float]:
        """Get the center position of the player"""
        return (self.x + self.width // 2, self.y + self.height // 2)

    def draw(self, screen: pygame.Surface):
        """Draw the player sprite"""
        current_sprite = self.sprites[self.frame][self.direction]
        screen.blit(current_sprite, (int(self.x), int(self.y)))


class Bullet:
    """Projectile fired by the player"""

    def __init__(self, start_x: float, start_y: float, target_x: float, target_y: float, image: pygame.Surface):
        self.x = float(start_x)
        self.y = float(start_y)
        self.speed = Config.BULLET_SPEED
        self.image = image
        self.width = image.get_width()
        self.height = image.get_height()

        # Calculate direction vector
        dx = target_x - start_x
        dy = target_y - start_y
        distance = math.sqrt(dx * dx + dy * dy)

        # Normalize direction vector
        if distance > 0:
            self.vel_x = (dx / distance) * self.speed
            self.vel_y = (dy / distance) * self.speed
        else:
            self.vel_x = 0
            self.vel_y = 0

    def update(self):
        """Update bullet position"""
        self.x += self.vel_x
        self.y += self.vel_y

    def is_off_screen(self) -> bool:
        """Check if bullet is off screen"""
        return (self.x < -self.width or self.x > Config.WINDOW_WIDTH or
                self.y < -self.height or self.y > Config.WINDOW_HEIGHT)

    def get_rect(self) -> pygame.Rect:
        """Get collision rectangle"""
        return pygame.Rect(int(self.x), int(self.y), self.width, self.height)

    def draw(self, screen: pygame.Surface):
        """Draw the bullet"""
        screen.blit(self.image, (int(self.x), int(self.y)))

class Goblin:
    """Enemy that chases the player"""

    def __init__(self, x: float, y: float, frames: List[pygame.Surface], frames_flipped: List[pygame.Surface]):
        self.x = float(x)
        self.y = float(y)
        self.speed = Config.GOBLIN_SPEED
        self.facing_left = False
        self.animation_frame = 0
        self.animation_timer = 0
        self.frames = frames
        self.frames_flipped = frames_flipped

        # Size properties (assuming all frames have same size)
        self.width = frames[0].get_width()
        self.height = frames[0].get_height()

    def update(self, player_x: float, player_y: float):
        """Update goblin position and animation to chase player"""
        # Calculate direction to player
        dx = player_x - self.x
        dy = player_y - self.y
        distance = math.sqrt(dx * dx + dy * dy)

        if distance > 0:
            # Normalize and apply speed
            move_x = (dx / distance) * self.speed
            move_y = (dy / distance) * self.speed

            # Update position
            self.x += move_x
            self.y += move_y

            # Determine facing direction based on horizontal movement
            if move_x < 0 and not self.facing_left:
                self.facing_left = True
            elif move_x > 0 and self.facing_left:
                self.facing_left = False

        # Update animation
        self.animation_timer += 1
        if self.animation_timer >= Config.GOBLIN_ANIMATION_SPEED:
            self.animation_timer = 0
            self.animation_frame = (self.animation_frame + 1) % len(self.frames)

    def get_rect(self) -> pygame.Rect:
        """Get collision rectangle"""
        return pygame.Rect(int(self.x), int(self.y), self.width, self.height)

    def draw(self, screen: pygame.Surface):
        """Draw the goblin with correct facing direction"""
        if self.facing_left:
            current_frame = self.frames_flipped[self.animation_frame]
        else:
            current_frame = self.frames[self.animation_frame]
        screen.blit(current_frame, (int(self.x), int(self.y)))

class Particle:
    """Explosion particle effect"""

    def __init__(self, x: float, y: float):
        self.x = float(x)
        self.y = float(y)

        # Random velocity for explosion effect
        angle = random.uniform(0, 2 * math.pi)
        speed = random.uniform(Config.PARTICLE_MIN_SPEED, Config.PARTICLE_MAX_SPEED)
        self.vel_x = math.cos(angle) * speed
        self.vel_y = math.sin(angle) * speed

        # Random size and lifetime
        self.size = random.randint(Config.PARTICLE_MIN_SIZE, Config.PARTICLE_MAX_SIZE)
        self.lifetime = random.randint(Config.PARTICLE_MIN_LIFETIME, Config.PARTICLE_MAX_LIFETIME)
        self.max_lifetime = self.lifetime

    def update(self):
        """Update particle position and lifetime"""
        self.x += self.vel_x
        self.y += self.vel_y
        # Apply deceleration
        self.vel_x *= 0.98
        self.vel_y *= 0.98
        self.lifetime -= 1

    def is_dead(self) -> bool:
        """Check if particle should be removed"""
        return self.lifetime <= 0

    def draw(self, screen: pygame.Surface):
        """Draw the particle with fading effect"""
        # Fade out as lifetime decreases
        alpha = int(255 * (self.lifetime / self.max_lifetime))
        color = (*Config.RED, alpha)

        # Create a surface with per-pixel alpha for the particle
        particle_surface = pygame.Surface((self.size * 2, self.size * 2), pygame.SRCALPHA)
        pygame.draw.circle(particle_surface, color, (self.size, self.size), self.size)
        screen.blit(particle_surface, (int(self.x - self.size), int(self.y - self.size)))
# =============================================================================
# COLLISION AND PHYSICS SYSTEMS
# =============================================================================

def check_tree_collision(x: float, y: float, width: int, height: int, tree_grid_positions: set) -> bool:
    """Check if a rectangle collides with any tree positions"""
    left_grid = int(x) // Config.TILE_SIZE
    right_grid = int(x + width - 1) // Config.TILE_SIZE
    top_grid = int(y) // Config.TILE_SIZE
    bottom_grid = int(y + height - 1) // Config.TILE_SIZE

    for grid_x in range(left_grid, right_grid + 1):
        for grid_y in range(top_grid, bottom_grid + 1):
            if (grid_x, grid_y) in tree_grid_positions:
                return True
    return False


def create_explosion_particles(x: float, y: float) -> List[Particle]:
    """Create explosion particles at the given position"""
    particles = []
    particle_count = random.randint(Config.PARTICLE_MIN_COUNT, Config.PARTICLE_MAX_COUNT)
    for _ in range(particle_count):
        particle = Particle(x, y)
        particles.append(particle)
    return particles


# =============================================================================
# MAP GENERATION
# =============================================================================

class MapGenerator:
    """Handles map generation and management"""

    def __init__(self, grass_tiles: List[pygame.Surface]):
        self.grass_tiles = grass_tiles
        self.background_map = []
        self.tree_positions = []
        self.tree_grid_positions = set()
        self.spawn_points = []

    def generate_map(self):
        """Generate the complete game map"""
        self._generate_background()
        self._generate_trees()
        self._generate_spawn_points()

    def _generate_background(self):
        """Generate random grass background"""
        self.background_map = []
        for row in range(Config.MAP_HEIGHT):
            map_row = []
            for col in range(Config.MAP_WIDTH):
                grass_tile_index = random.randint(0, len(self.grass_tiles) - 1)
                map_row.append(grass_tile_index)
            self.background_map.append(map_row)

    def _generate_trees(self):
        """Generate tree border with gaps"""
        self.tree_positions = []
        self.tree_grid_positions = set()

        # Calculate center positions for each edge (3 squares wide gap)
        center_top = Config.MAP_WIDTH // 2
        center_bottom = Config.MAP_WIDTH // 2
        center_left = Config.MAP_HEIGHT // 2
        center_right = Config.MAP_HEIGHT // 2

        # Top edge
        for x in range(Config.MAP_WIDTH):
            if not (center_top - 1 <= x <= center_top + 1):
                self.tree_grid_positions.add((x, 0))
                self.tree_positions.append((x * Config.TILE_SIZE, 0))

        # Bottom edge
        for x in range(Config.MAP_WIDTH):
            if not (center_bottom - 1 <= x <= center_bottom + 1):
                self.tree_grid_positions.add((x, Config.MAP_HEIGHT - 1))
                self.tree_positions.append((x * Config.TILE_SIZE, (Config.MAP_HEIGHT - 1) * Config.TILE_SIZE))

        # Left edge
        for y in range(1, Config.MAP_HEIGHT - 1):
            if not (center_left - 1 <= y <= center_left + 1):
                self.tree_grid_positions.add((0, y))
                self.tree_positions.append((0, y * Config.TILE_SIZE))

        # Right edge
        for y in range(1, Config.MAP_HEIGHT - 1):
            if not (center_right - 1 <= y <= center_right + 1):
                self.tree_grid_positions.add((Config.MAP_WIDTH - 1, y))
                self.tree_positions.append(((Config.MAP_WIDTH - 1) * Config.TILE_SIZE, y * Config.TILE_SIZE))

    def _generate_spawn_points(self):
        """Generate spawn points at the center of each gap"""
        self.spawn_points = [
            (Config.MAP_WIDTH // 2 * Config.TILE_SIZE, 0),  # Top gap
            (Config.MAP_WIDTH // 2 * Config.TILE_SIZE, (Config.MAP_HEIGHT - 1) * Config.TILE_SIZE),  # Bottom gap
            (0, Config.MAP_HEIGHT // 2 * Config.TILE_SIZE),  # Left gap
            ((Config.MAP_WIDTH - 1) * Config.TILE_SIZE, Config.MAP_HEIGHT // 2 * Config.TILE_SIZE)  # Right gap
        ]

# =============================================================================
# MAIN GAME CLASS
# =============================================================================

class Game:
    """Main game class that manages game state and loop"""

    def __init__(self):
        # Initialize pygame
        pygame.init()
        self.screen = pygame.display.set_mode((Config.WINDOW_WIDTH, Config.WINDOW_HEIGHT))
        pygame.display.set_caption("Top Down Shooter")
        self.clock = pygame.time.Clock()
        self.running = True

        # Initialize asset manager and load assets
        self.asset_manager = AssetManager()
        self.asset_manager.load_all_assets()

        # Initialize map generator and generate map
        self.map_generator = MapGenerator(self.asset_manager.grass_tiles)
        self.map_generator.generate_map()

        # Initialize player
        player_start_x = Config.WINDOW_WIDTH // 2 - Config.SPRITE_WIDTH // 2
        player_start_y = Config.WINDOW_HEIGHT // 2 - Config.SPRITE_HEIGHT // 2
        self.player = Player(player_start_x, player_start_y, self.asset_manager.character_sprites)

        # Initialize game entities
        self.bullets = []
        self.goblins = []
        self.particles = []

        # Game timers
        self.goblin_spawn_timer = 0

        print("Pygame window initialized. Use WASD to move, mouse click to shoot, ESC to close.")

    def handle_events(self):
        """Handle pygame events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    print("ESC pressed - closing window")
                    self.running = False
            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # Left mouse button
                    self._shoot_bullet(event.pos)

    def _shoot_bullet(self, target_pos: Tuple[int, int]):
        """Create and fire a bullet towards the target position"""
        player_center = self.player.get_center()
        bullet = Bullet(
            player_center[0], player_center[1],
            target_pos[0], target_pos[1],
            self.asset_manager.bullet_image
        )
        self.bullets.append(bullet)

    def update(self):
        """Update all game entities"""
        # Update player
        keys = pygame.key.get_pressed()
        tree_collision_func = lambda x, y, w, h: check_tree_collision(x, y, w, h, self.map_generator.tree_grid_positions)
        self.player.update(keys, tree_collision_func)

        # Update bullets
        for bullet in self.bullets[:]:
            bullet.update()
            if bullet.is_off_screen():
                self.bullets.remove(bullet)

        # Spawn goblins
        self.goblin_spawn_timer += 1
        if self.goblin_spawn_timer >= Config.GOBLIN_SPAWN_INTERVAL:
            self.goblin_spawn_timer = 0
            spawn_pos = random.choice(self.map_generator.spawn_points)
            goblin = Goblin(
                spawn_pos[0], spawn_pos[1],
                self.asset_manager.goblin_frames,
                self.asset_manager.goblin_frames_flipped
            )
            self.goblins.append(goblin)

        # Update goblins
        player_center = self.player.get_center()
        for goblin in self.goblins:
            goblin.update(player_center[0], player_center[1])

        # Check bullet-goblin collisions
        self._check_collisions()

        # Update particles
        for particle in self.particles[:]:
            particle.update()
            if particle.is_dead():
                self.particles.remove(particle)

    def _check_collisions(self):
        """Check and handle bullet-goblin collisions"""
        for bullet in self.bullets[:]:
            bullet_rect = bullet.get_rect()
            for goblin in self.goblins[:]:
                goblin_rect = goblin.get_rect()
                if bullet_rect.colliderect(goblin_rect):
                    # Create explosion particles
                    explosion_x = goblin.x + goblin.width // 2
                    explosion_y = goblin.y + goblin.height // 2
                    explosion_particles = create_explosion_particles(explosion_x, explosion_y)
                    self.particles.extend(explosion_particles)

                    # Remove bullet and goblin
                    self.bullets.remove(bullet)
                    self.goblins.remove(goblin)
                    break

    def render(self):
        """Render all game elements"""
        # Fill screen with black
        self.screen.fill(Config.BLACK)

        # Draw background grass tiles
        for row in range(Config.MAP_HEIGHT):
            for col in range(Config.MAP_WIDTH):
                tile_x = col * Config.TILE_SIZE
                tile_y = row * Config.TILE_SIZE
                # Only draw tiles that are visible on screen
                if tile_x < Config.WINDOW_WIDTH and tile_y < Config.WINDOW_HEIGHT:
                    grass_tile_index = self.map_generator.background_map[row][col]
                    grass_tile = self.asset_manager.grass_tiles[grass_tile_index]
                    self.screen.blit(grass_tile, (tile_x, tile_y))

        # Draw trees
        for tree_x, tree_y in self.map_generator.tree_positions:
            if tree_x < Config.WINDOW_WIDTH and tree_y < Config.WINDOW_HEIGHT:
                self.screen.blit(self.asset_manager.tree_tile, (tree_x, tree_y))

        # Draw goblins
        for goblin in self.goblins:
            goblin.draw(self.screen)

        # Draw player
        self.player.draw(self.screen)

        # Draw bullets
        for bullet in self.bullets:
            bullet.draw(self.screen)

        # Draw particles
        for particle in self.particles:
            particle.draw(self.screen)

        # Update display
        pygame.display.flip()

    def run(self):
        """Main game loop"""
        while self.running:
            self.handle_events()
            self.update()
            self.render()
            self.clock.tick(Config.FPS)

        # Cleanup
        pygame.quit()
        sys.exit()


# =============================================================================
# MAIN FUNCTION
# =============================================================================

def main():
    """Initialize and run the game"""
    game = Game()
    game.run()


if __name__ == "__main__":
    main()


