import pygame
import sys
import os
import random
import math

# Initialize pygame
pygame.init()

# Calculate optimal window size to fit the map with trees
TILE_SIZE = 16
TILES_WIDTH = 600 // TILE_SIZE + 2  # Original width in tiles + border trees
TILES_HEIGHT = 400 // TILE_SIZE + 2  # Original height in tiles + border trees
WINDOW_WIDTH = TILES_WIDTH * TILE_SIZE
WINDOW_HEIGHT = TILES_HEIGHT * TILE_SIZE

# Map dimensions
MAP_WIDTH = TILES_WIDTH  # Use calculated tile dimensions
MAP_HEIGHT = TILES_HEIGHT

# Set up the display
screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
pygame.display.set_caption("Top Down Shooter")

# Define colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)

# Load spritesheet
spritesheet_path = os.path.join("Assets", "spritesheet.png")
spritesheet = pygame.image.load(spritesheet_path)

# Character sprite dimensions and position in spritesheet
SPRITE_START_X = 1
SPRITE_START_Y = 374
SPRITE_SECTION_WIDTH = 240  # 240 - 1
SPRITE_SECTION_HEIGHT = 125  # 499 - 374
SPRITE_COLS = 8  # 8 directions
SPRITE_ROWS = 3  # 3 animation frames
SPRITE_WIDTH = SPRITE_SECTION_WIDTH // SPRITE_COLS  # ~30 pixels
SPRITE_HEIGHT = SPRITE_SECTION_HEIGHT // SPRITE_ROWS  # ~42 pixels

# Function to apply color key with tolerance
def apply_color_key_with_tolerance(surface, key_color, tolerance=30):
    """Apply color key transparency with tolerance for similar colors"""
    # Convert surface to per-pixel alpha format
    surface = surface.convert_alpha()

    # Get surface dimensions
    width, height = surface.get_size()

    # Process each pixel
    for x in range(width):
        for y in range(height):
            pixel_color = surface.get_at((x, y))

            # Calculate color difference (Manhattan distance in RGB space)
            color_diff = (abs(pixel_color[0] - key_color[0]) +
                         abs(pixel_color[1] - key_color[1]) +
                         abs(pixel_color[2] - key_color[2]))

            # If color is similar enough to key color, make it transparent
            if color_diff <= tolerance:
                surface.set_at((x, y), (pixel_color[0], pixel_color[1], pixel_color[2], 0))

    return surface

# Function to check if a position collides with a tree
def check_tree_collision(x, y, width, height):
    """Check if a rectangle collides with any tree positions"""
    # Get the grid positions that the character rectangle occupies
    left_grid = x // TILE_SIZE
    right_grid = (x + width - 1) // TILE_SIZE
    top_grid = y // TILE_SIZE
    bottom_grid = (y + height - 1) // TILE_SIZE

    # Check all grid positions the character occupies
    for grid_x in range(left_grid, right_grid + 1):
        for grid_y in range(top_grid, bottom_grid + 1):
            if (grid_x, grid_y) in tree_grid_positions:
                return True
    return False

# Extract character sprites
character_sprites = []
for row in range(SPRITE_ROWS):
    sprite_row = []
    for col in range(SPRITE_COLS):
        x = SPRITE_START_X + col * SPRITE_WIDTH
        y = SPRITE_START_Y + row * SPRITE_HEIGHT
        sprite_rect = pygame.Rect(x, y, SPRITE_WIDTH, SPRITE_HEIGHT)
        sprite = spritesheet.subsurface(sprite_rect).copy()

        # Set alpha channel using corner pixel color with tolerance
        corner_color = sprite.get_at((0, 0))  # Get top-left corner pixel color
        sprite = apply_color_key_with_tolerance(sprite, corner_color, tolerance=30)

        sprite_row.append(sprite)
    character_sprites.append(sprite_row)

# Character position and state
character_x = WINDOW_WIDTH // 2 - SPRITE_WIDTH // 2
character_y = WINDOW_HEIGHT // 2 - SPRITE_HEIGHT // 2
character_direction = 4  # Down direction (0=up, going clockwise, so 4=down)
character_frame = 1  # Stationary state (middle row)
character_speed = 3  # Movement speed in pixels per frame

# Animation variables
animation_timer = 0
animation_speed = 10  # Frames between animation changes
current_animation_frame = 0  # 0 for row 1, 1 for row 3 when moving

# Bullets list
bullets = []

# Goblins list and spawning
goblins = []
goblin_spawn_timer = 0
goblin_spawn_interval = 120  # 2 seconds at 60 FPS

# Define spawn points (middle of each gap in tree border)
spawn_points = [
    # Top gap (center of top edge)
    (MAP_WIDTH // 2 * TILE_SIZE, 0),
    # Bottom gap (center of bottom edge)
    (MAP_WIDTH // 2 * TILE_SIZE, (MAP_HEIGHT - 1) * TILE_SIZE),
    # Left gap (center of left edge)
    (0, MAP_HEIGHT // 2 * TILE_SIZE),
    # Right gap (center of right edge)
    ((MAP_WIDTH - 1) * TILE_SIZE, MAP_HEIGHT // 2 * TILE_SIZE)
]

# Direction mapping for WASD keys (clockwise from up)
# 0=up, 1=up-right, 2=right, 3=down-right, 4=down, 5=down-left, 6=left, 7=up-left
DIRECTION_UP = 0
DIRECTION_RIGHT = 2
DIRECTION_DOWN = 4
DIRECTION_LEFT = 6

# Load grass tileset
grass_tileset_path = os.path.join("Assets", "Grass.png")
grass_tileset = pygame.image.load(grass_tileset_path)

# Tile dimensions (TILE_SIZE already defined above)
TILES_PER_ROW = grass_tileset.get_width() // TILE_SIZE
TILES_PER_COL = grass_tileset.get_height() // TILE_SIZE

# Extract grass tiles (all columns except the last one)
grass_tiles = []
for row in range(TILES_PER_COL):
    for col in range(TILES_PER_ROW - 1):  # Exclude last column (trees)
        x = col * TILE_SIZE
        y = row * TILE_SIZE
        tile_rect = pygame.Rect(x, y, TILE_SIZE, TILE_SIZE)
        tile = grass_tileset.subsurface(tile_rect)
        grass_tiles.append(tile)

# Extract tree tile (top row of rightmost column)
tree_x = (TILES_PER_ROW - 1) * TILE_SIZE
tree_y = 0
tree_rect = pygame.Rect(tree_x, tree_y, TILE_SIZE, TILE_SIZE)
tree_tile = grass_tileset.subsurface(tree_rect)

# Load bullet sprite
bullet_image_path = os.path.join("Assets", "bullet.png")
bullet_image = pygame.image.load(bullet_image_path).convert_alpha()

# Apply alpha channel to bullet using corner pixel color
corner_color = bullet_image.get_at((0, 0))
bullet_image = apply_color_key_with_tolerance(bullet_image, corner_color, tolerance=30)

# Load goblin spritesheet
goblin_spritesheet_path = os.path.join("Assets", "goblin.png")
goblin_spritesheet = pygame.image.load(goblin_spritesheet_path)

# Extract goblin animation frames (8 frames, 1 row)
GOBLIN_FRAMES = 8
GOBLIN_WIDTH = goblin_spritesheet.get_width() // GOBLIN_FRAMES
GOBLIN_HEIGHT = goblin_spritesheet.get_height()

goblin_frames = []
for frame in range(GOBLIN_FRAMES):
    x = frame * GOBLIN_WIDTH
    frame_rect = pygame.Rect(x, 0, GOBLIN_WIDTH, GOBLIN_HEIGHT)
    frame_surface = goblin_spritesheet.subsurface(frame_rect).copy()

    # Apply alpha channel to goblin frames
    corner_color = frame_surface.get_at((0, 0))
    frame_surface = apply_color_key_with_tolerance(frame_surface, corner_color, tolerance=30)

    goblin_frames.append(frame_surface)

# Create flipped versions for left movement
goblin_frames_flipped = []
for frame in goblin_frames:
    flipped_frame = pygame.transform.flip(frame, True, False)  # Flip horizontally
    goblin_frames_flipped.append(flipped_frame)

# Bullet class
class Bullet:
    def __init__(self, start_x, start_y, target_x, target_y):
        self.x = float(start_x)
        self.y = float(start_y)
        self.speed = 8  # Bullet speed

        # Calculate direction vector
        dx = target_x - start_x
        dy = target_y - start_y
        distance = math.sqrt(dx * dx + dy * dy)

        # Normalize direction vector
        if distance > 0:
            self.vel_x = (dx / distance) * self.speed
            self.vel_y = (dy / distance) * self.speed
        else:
            self.vel_x = 0
            self.vel_y = 0

        self.image = bullet_image
        self.width = bullet_image.get_width()
        self.height = bullet_image.get_height()

    def update(self):
        self.x += self.vel_x
        self.y += self.vel_y

    def is_off_screen(self):
        return (self.x < -self.width or self.x > WINDOW_WIDTH or
                self.y < -self.height or self.y > WINDOW_HEIGHT)

    def draw(self, screen):
        screen.blit(self.image, (int(self.x), int(self.y)))

# Goblin class
class Goblin:
    def __init__(self, x, y):
        self.x = float(x)
        self.y = float(y)
        self.speed = 1.5  # Goblin movement speed
        self.facing_left = False  # Track which direction goblin is facing
        self.animation_frame = 0
        self.animation_timer = 0
        self.animation_speed = 8  # Frames between animation changes
        self.width = GOBLIN_WIDTH
        self.height = GOBLIN_HEIGHT

    def update(self, player_x, player_y):
        # Calculate direction to player
        dx = player_x - self.x
        dy = player_y - self.y
        distance = math.sqrt(dx * dx + dy * dy)

        if distance > 0:
            # Normalize and apply speed
            move_x = (dx / distance) * self.speed
            move_y = (dy / distance) * self.speed

            # Update position
            self.x += move_x
            self.y += move_y

            # Determine facing direction based on horizontal movement
            if move_x < 0 and not self.facing_left:
                self.facing_left = True
            elif move_x > 0 and self.facing_left:
                self.facing_left = False

        # Update animation
        self.animation_timer += 1
        if self.animation_timer >= self.animation_speed:
            self.animation_timer = 0
            self.animation_frame = (self.animation_frame + 1) % GOBLIN_FRAMES

    def draw(self, screen):
        # Choose the correct frame set based on facing direction
        if self.facing_left:
            current_frame = goblin_frames_flipped[self.animation_frame]
        else:
            current_frame = goblin_frames[self.animation_frame]

        screen.blit(current_frame, (int(self.x), int(self.y)))

    def get_rect(self):
        return pygame.Rect(int(self.x), int(self.y), self.width, self.height)

# Generate background map
background_map = []
for row in range(MAP_HEIGHT):
    map_row = []
    for col in range(MAP_WIDTH):
        # Choose random grass tile
        grass_tile_index = random.randint(0, len(grass_tiles) - 1)
        map_row.append(grass_tile_index)
    background_map.append(map_row)

# Generate tree positions around the edge with gaps in the center of each edge
tree_positions = []
tree_grid_positions = set()  # Store grid positions for collision detection

# Calculate center positions for each edge (3 squares wide gap)
center_top = MAP_WIDTH // 2
center_bottom = MAP_WIDTH // 2
center_left = MAP_HEIGHT // 2
center_right = MAP_HEIGHT // 2

# Top edge (y = 0)
for x in range(MAP_WIDTH):
    if not (center_top - 1 <= x <= center_top + 1):  # Skip 3 center squares
        tree_grid_positions.add((x, 0))
        tree_positions.append((x * TILE_SIZE, 0 * TILE_SIZE))

# Bottom edge (y = MAP_HEIGHT - 1)
for x in range(MAP_WIDTH):
    if not (center_bottom - 1 <= x <= center_bottom + 1):  # Skip 3 center squares
        tree_grid_positions.add((x, MAP_HEIGHT - 1))
        tree_positions.append((x * TILE_SIZE, (MAP_HEIGHT - 1) * TILE_SIZE))

# Left edge (x = 0)
for y in range(1, MAP_HEIGHT - 1):  # Skip corners (already covered by top/bottom)
    if not (center_left - 1 <= y <= center_left + 1):  # Skip 3 center squares
        tree_grid_positions.add((0, y))
        tree_positions.append((0 * TILE_SIZE, y * TILE_SIZE))

# Right edge (x = MAP_WIDTH - 1)
for y in range(1, MAP_HEIGHT - 1):  # Skip corners (already covered by top/bottom)
    if not (center_right - 1 <= y <= center_right + 1):  # Skip 3 center squares
        tree_grid_positions.add((MAP_WIDTH - 1, y))
        tree_positions.append(((MAP_WIDTH - 1) * TILE_SIZE, y * TILE_SIZE))

# Game loop
running = True
clock = pygame.time.Clock()

print("Pygame window initialized. Use WASD to move, mouse click to shoot, ESC to close.")

while running:
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False
        elif event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE:
                # ESC key closes the window
                print("ESC pressed - closing window")
                running = False
        elif event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left mouse button
                # Get mouse position
                mouse_x, mouse_y = event.pos

                # Calculate bullet start position (center of character)
                bullet_start_x = character_x + SPRITE_WIDTH // 2
                bullet_start_y = character_y + SPRITE_HEIGHT // 2

                # Create and add bullet
                bullet = Bullet(bullet_start_x, bullet_start_y, mouse_x, mouse_y)
                bullets.append(bullet)

    # Handle continuous key presses for movement
    keys = pygame.key.get_pressed()
    moving = False

    # Store original position for collision checking
    original_x = character_x
    original_y = character_y

    # WASD movement
    if keys[pygame.K_w]:  # W - Move up
        new_y = character_y - character_speed
        if not check_tree_collision(character_x, new_y, SPRITE_WIDTH, SPRITE_HEIGHT):
            character_y = new_y
            character_direction = DIRECTION_UP
            moving = True
        else:
            character_direction = DIRECTION_UP  # Still face the direction even if blocked

    if keys[pygame.K_s]:  # S - Move down
        new_y = character_y + character_speed
        if not check_tree_collision(character_x, new_y, SPRITE_WIDTH, SPRITE_HEIGHT):
            character_y = new_y
            character_direction = DIRECTION_DOWN
            moving = True
        else:
            character_direction = DIRECTION_DOWN  # Still face the direction even if blocked

    if keys[pygame.K_a]:  # A - Move left
        new_x = character_x - character_speed
        if not check_tree_collision(new_x, character_y, SPRITE_WIDTH, SPRITE_HEIGHT):
            character_x = new_x
            character_direction = DIRECTION_LEFT
            moving = True
        else:
            character_direction = DIRECTION_LEFT  # Still face the direction even if blocked

    if keys[pygame.K_d]:  # D - Move right
        new_x = character_x + character_speed
        if not check_tree_collision(new_x, character_y, SPRITE_WIDTH, SPRITE_HEIGHT):
            character_x = new_x
            character_direction = DIRECTION_RIGHT
            moving = True
        else:
            character_direction = DIRECTION_RIGHT  # Still face the direction even if blocked

    # Keep character within screen bounds
    character_x = max(0, min(character_x, WINDOW_WIDTH - SPRITE_WIDTH))
    character_y = max(0, min(character_y, WINDOW_HEIGHT - SPRITE_HEIGHT))

    # Set animation frame based on movement
    if moving:
        # Toggle between animation frames when moving
        animation_timer += 1
        if animation_timer >= animation_speed:
            animation_timer = 0
            current_animation_frame = 1 - current_animation_frame  # Toggle between 0 and 1

        # Use rows 0 and 2 for movement animation (indices 0 and 2)
        character_frame = 0 if current_animation_frame == 0 else 2
    else:
        character_frame = 1  # Use second row (index 1) for stationary state
        animation_timer = 0  # Reset animation timer when not moving

    # Update bullets
    for bullet in bullets[:]:  # Use slice copy to safely modify list during iteration
        bullet.update()
        if bullet.is_off_screen():
            bullets.remove(bullet)

    # Spawn goblins
    goblin_spawn_timer += 1
    if goblin_spawn_timer >= goblin_spawn_interval:
        goblin_spawn_timer = 0
        # Choose random spawn point
        spawn_x, spawn_y = random.choice(spawn_points)
        goblin = Goblin(spawn_x, spawn_y)
        goblins.append(goblin)

    # Update goblins
    player_center_x = character_x + SPRITE_WIDTH // 2
    player_center_y = character_y + SPRITE_HEIGHT // 2

    for goblin in goblins:
        goblin.update(player_center_x, player_center_y)

    # Fill the screen with black
    screen.fill(BLACK)

    # Draw background grass tiles
    for row in range(MAP_HEIGHT):
        for col in range(MAP_WIDTH):
            tile_x = col * TILE_SIZE
            tile_y = row * TILE_SIZE
            # Only draw tiles that are visible on screen
            if tile_x < WINDOW_WIDTH and tile_y < WINDOW_HEIGHT:
                grass_tile_index = background_map[row][col]
                grass_tile = grass_tiles[grass_tile_index]
                screen.blit(grass_tile, (tile_x, tile_y))

    # Draw trees
    for tree_x, tree_y in tree_positions:
        if tree_x < WINDOW_WIDTH and tree_y < WINDOW_HEIGHT:
            screen.blit(tree_tile, (tree_x, tree_y))

    # Draw goblins
    for goblin in goblins:
        goblin.draw(screen)

    # Draw the character sprite
    current_sprite = character_sprites[character_frame][character_direction]
    screen.blit(current_sprite, (character_x, character_y))

    # Draw bullets
    for bullet in bullets:
        bullet.draw(screen)

    # Update the display
    pygame.display.flip()

    # Control frame rate
    clock.tick(60)

# Quit pygame
pygame.quit()
sys.exit()