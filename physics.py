"""
Physics and collision detection systems for the game.

This module contains all collision detection functions and physics-related
utilities used throughout the game.
"""

import random
import math
from typing import List, Set, Tuple
from config import Config


def check_tree_collision(x: float, y: float, width: int, height: int, tree_grid_positions: Set[Tuple[int, int]]) -> bool:
    """
    Check if a rectangle collides with any tree positions.
    
    Args:
        x: X position of the rectangle
        y: Y position of the rectangle
        width: Width of the rectangle
        height: Height of the rectangle
        tree_grid_positions: Set of (grid_x, grid_y) positions containing trees
    
    Returns:
        True if collision detected, False otherwise
    """
    # Convert pixel coordinates to grid coordinates
    left_grid = int(x) // Config.TILE_SIZE
    right_grid = int(x + width - 1) // Config.TILE_SIZE
    top_grid = int(y) // Config.TILE_SIZE
    bottom_grid = int(y + height - 1) // Config.TILE_SIZE
    
    # Check all grid positions the rectangle occupies
    for grid_x in range(left_grid, right_grid + 1):
        for grid_y in range(top_grid, bottom_grid + 1):
            if (grid_x, grid_y) in tree_grid_positions:
                return True
    return False


def check_bounds_collision(x: float, y: float, width: int, height: int) -> Tuple[float, float]:
    """
    Keep an entity within screen bounds.
    
    Args:
        x: Current X position
        y: Current Y position
        width: Entity width
        height: Entity height
    
    Returns:
        Tuple of (clamped_x, clamped_y) positions
    """
    clamped_x = max(0, min(x, Config.WINDOW_WIDTH - width))
    clamped_y = max(0, min(y, Config.WINDOW_HEIGHT - height))
    return clamped_x, clamped_y


def calculate_direction_vector(start_x: float, start_y: float, target_x: float, target_y: float) -> Tuple[float, float]:
    """
    Calculate normalized direction vector from start to target position.
    
    Args:
        start_x: Starting X position
        start_y: Starting Y position
        target_x: Target X position
        target_y: Target Y position
    
    Returns:
        Tuple of (normalized_dx, normalized_dy)
    """
    dx = target_x - start_x
    dy = target_y - start_y
    distance = math.sqrt(dx * dx + dy * dy)
    
    if distance > 0:
        return dx / distance, dy / distance
    else:
        return 0.0, 0.0


def is_off_screen(x: float, y: float, width: int, height: int) -> bool:
    """
    Check if an entity is completely off screen.
    
    Args:
        x: Entity X position
        y: Entity Y position
        width: Entity width
        height: Entity height
    
    Returns:
        True if entity is off screen, False otherwise
    """
    return (x < -width or x > Config.WINDOW_WIDTH or
            y < -height or y > Config.WINDOW_HEIGHT)


def create_explosion_particles(x: float, y: float):
    """
    Create explosion particles at the given position.

    Args:
        x: Explosion center X position
        y: Explosion center Y position

    Returns:
        List of Particle objects
    """
    # Import here to avoid circular imports
    from entities import Particle

    particles = []
    particle_count = random.randint(Config.PARTICLE_MIN_COUNT, Config.PARTICLE_MAX_COUNT)
    for _ in range(particle_count):
        particle = Particle(x, y)
        particles.append(particle)
    return particles


def apply_deceleration(velocity_x: float, velocity_y: float, deceleration_factor: float = 0.98) -> Tuple[float, float]:
    """
    Apply deceleration to velocity components.
    
    Args:
        velocity_x: Current X velocity
        velocity_y: Current Y velocity
        deceleration_factor: Factor to multiply velocities by (0.0 to 1.0)
    
    Returns:
        Tuple of (new_velocity_x, new_velocity_y)
    """
    return velocity_x * deceleration_factor, velocity_y * deceleration_factor


def calculate_distance(x1: float, y1: float, x2: float, y2: float) -> float:
    """
    Calculate Euclidean distance between two points.
    
    Args:
        x1: First point X coordinate
        y1: First point Y coordinate
        x2: Second point X coordinate
        y2: Second point Y coordinate
    
    Returns:
        Distance between the points
    """
    dx = x2 - x1
    dy = y2 - y1
    return math.sqrt(dx * dx + dy * dy)


def generate_random_velocity(min_speed: float, max_speed: float) -> Tuple[float, float]:
    """
    Generate random velocity vector with speed in given range.
    
    Args:
        min_speed: Minimum speed magnitude
        max_speed: Maximum speed magnitude
    
    Returns:
        Tuple of (velocity_x, velocity_y)
    """
    angle = random.uniform(0, 2 * math.pi)
    speed = random.uniform(min_speed, max_speed)
    velocity_x = math.cos(angle) * speed
    velocity_y = math.sin(angle) * speed
    return velocity_x, velocity_y
